<?php
require_once('wp-load.php');

// Test if we can reach the AJAX endpoint
$ajax_url = admin_url('admin-ajax.php');
$nonce = wp_create_nonce('video_admin_nonce');

echo "AJAX URL: " . $ajax_url . "\n";
echo "Nonce: " . $nonce . "\n";

// Test basic AJAX connectivity
$test_data = array(
    'action' => 'video_upload_file',
    'nonce' => $nonce
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $ajax_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($test_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: " . $http_code . "\n";
echo "Response: " . $response . "\n";
if ($error) {
    echo "cURL Error: " . $error . "\n";
}
?>
