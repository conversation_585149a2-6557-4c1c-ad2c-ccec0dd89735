<?php
require_once('wp-load.php');

echo "Current user ID: " . get_current_user_id() . "\n";
echo "Is user logged in: " . (is_user_logged_in() ? 'Yes' : 'No') . "\n";
echo "Can manage options: " . (current_user_can('manage_options') ? 'Yes' : 'No') . "\n";
echo "Is admin: " . (is_admin() ? 'Yes' : 'No') . "\n";

// Check if the AJAX action exists
global $wp_filter;
if (isset($wp_filter['wp_ajax_video_upload_file'])) {
    echo "AJAX action 'wp_ajax_video_upload_file' is registered\n";
    print_r($wp_filter['wp_ajax_video_upload_file']);
} else {
    echo "AJAX action 'wp_ajax_video_upload_file' is NOT registered\n";
}

// Check if VideoBookingAdmin class exists
if (class_exists('VideoBookingAdmin')) {
    echo "VideoBookingAdmin class exists\n";
} else {
    echo "VideoBookingAdmin class does NOT exist\n";
}
?>
