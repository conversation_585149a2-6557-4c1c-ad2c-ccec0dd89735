<?php
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Input Time: " . ini_get('max_input_time') . "\n";
echo "Max File Uploads: " . ini_get('max_file_uploads') . "\n";

// Check if .user.ini is being read
echo "\nUser INI directive: " . ini_get('user_ini.filename') . "\n";
echo "User INI cache TTL: " . ini_get('user_ini.cache_ttl') . "\n";

// Check WordPress upload limits
if (function_exists('wp_max_upload_size')) {
    echo "WordPress Max Upload Size: " . size_format(wp_max_upload_size()) . "\n";
}
?>
