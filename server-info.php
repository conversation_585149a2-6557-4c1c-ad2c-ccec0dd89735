<?php
header('Content-Type: text/plain');
echo "PHP_SAPI: ".PHP_SAPI."\n";
echo "Server Software: ".($_SERVER['SERVER_SOFTWARE'] ?? 'unknown')."\n";
echo "Gateway Interface: ".($_SERVER['GATEWAY_INTERFACE'] ?? 'unknown')."\n";
echo "Request Method: ".($_SERVER['REQUEST_METHOD'] ?? 'unknown')."\n";
echo "Content Length Limit Headers: \n";
foreach (['Content-Length','Content-Type','Transfer-Encoding'] as $h) {
    $key = 'HTTP_' . strtoupper(str_replace('-', '_', $h));
    if (isset($_SERVER[$key])) echo "  $h: ".$_SERVER[$key]."\n";
}
?>
