/**
 * Video Booking Admin Styles
 * 
 * @package VideoBooking
 */

/* Admin Container */
.video-admin-container {
    margin-top: 20px;
}

/* Form Styles */
.video-form {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.video-form .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.video-form .form-table td {
    padding: 15px 10px;
}

.video-form input.error,
.video-form textarea.error,
.video-form select.error {
    border-color: #d63638;
    box-shadow: 0 0 0 1px #d63638;
}

.error-message {
    color: #d63638;
    font-size: 13px;
    margin: 5px 0 0 0;
}

/* Thumbnail Upload */
.thumbnail-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.thumbnail-preview {
    min-height: 50px;
}

.thumbnail-preview img {
    border: 1px solid #ddd;
    border-radius: 4px;
    max-width: 200px;
    height: auto;
}

.upload-thumbnail,
.remove-thumbnail {
    align-self: flex-start;
}

/* Video Upload */
.video-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.video-preview {
    min-height: 30px;
    padding: 10px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.upload-video {
    align-self: flex-start;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-refunded {
    background: #d1ecf1;
    color: #0c5460;
}

/* Upload Status */
.upload-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.upload-uploaded {
    background: #d4edda;
    color: #155724;
}

.upload-pending {
    background: #fff3cd;
    color: #856404;
}

/* Early Bird Badge */
.early-bird-badge {
    background: #ff6b35;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 8px;
}

/* Table Styles */
.wp-list-table .column-thumbnail {
    width: 80px;
}

.wp-list-table .column-title {
    width: 25%;
}

.wp-list-table .column-pricing {
    width: 15%;
}

.wp-list-table .column-status {
    width: 10%;
}

.wp-list-table .column-upload-status {
    width: 15%;
}

.wp-list-table .column-created {
    width: 12%;
}

.wp-list-table .column-actions {
    width: 18%;
}

/* Button Styles */
.mark-uploaded {
    background: #007cba;
    color: white;
    border-color: #007cba;
    margin-left: 8px;
}

.mark-uploaded:hover {
    background: #005a87;
    border-color: #005a87;
}

.toggle-status {
    margin-right: 5px;
}

.delete-video {
    color: #d63638;
}

.delete-video:hover {
    color: #d63638;
    background: #f6f7f7;
}

/* Form Field Groups */
.early-bird-fields {
    transition: all 0.3s ease;
}

.early-bird-fields.hidden {
    display: none;
}

/* Responsive Design */
@media screen and (max-width: 782px) {
    .video-form .form-table th,
    .video-form .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .video-form .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .thumbnail-upload,
    .video-upload {
        align-items: flex-start;
    }
    
    .wp-list-table .column-actions {
        text-align: left;
    }
    
    .wp-list-table .button {
        display: block;
        margin: 2px 0;
        text-align: center;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notice Styles */
.notice {
    margin: 15px 0;
}

.notice p {
    margin: 0.5em 0;
    padding: 2px;
}

/* Video Preview in Admin */
.video-admin-preview {
    max-width: 300px;
    background: #000;
    border-radius: 4px;
    overflow: hidden;
}

.video-admin-preview video {
    width: 100%;
    height: auto;
}

/* Upload Progress */
.upload-progress {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.upload-progress-bar {
    height: 100%;
    background: #007cba;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.upload-progress-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Drag and Drop Upload */
.upload-dropzone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-dropzone:hover {
    border-color: #007cba;
    background: #f0f8ff;
}

.upload-dropzone.dragover {
    border-color: #007cba;
    background: #e6f3ff;
}

.upload-dropzone-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.upload-dropzone-subtext {
    font-size: 14px;
    color: #999;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.hidden { display: none; }
.show { display: block; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
