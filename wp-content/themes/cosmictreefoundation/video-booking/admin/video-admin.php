<?php

/**
 * Video Admin Interface
 *
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Admin Class
 */
class VideoBookingAdmin
{

    public function __construct()
    {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_video_save', array($this, 'save_video'));
        add_action('wp_ajax_video_delete', array($this, 'delete_video'));
        add_action('wp_ajax_video_toggle_status', array($this, 'toggle_video_status'));
        add_action('wp_ajax_video_upload_file', array($this, 'handle_video_upload'));
        // Chunked upload endpoints (works across servers/CDNs)
        add_action('wp_ajax_video_chunk_init', array($this, 'chunk_init'));
        add_action('wp_ajax_video_chunk_upload', array($this, 'chunk_upload'));
        add_action('wp_ajax_video_chunk_finish', array($this, 'chunk_finish'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu()
    {
        add_menu_page(
            'Video Booking',
            'Video Booking',
            'manage_options',
            'video-booking',
            array($this, 'admin_page'),
            'dashicons-video-alt3',
            30
        );

        add_submenu_page(
            'video-booking',
            'All Videos',
            'All Videos',
            'manage_options',
            'video-booking',
            array($this, 'admin_page')
        );

        add_submenu_page(
            'video-booking',
            'Add New Video',
            'Add New Video',
            'manage_options',
            'video-booking-add',
            array($this, 'add_video_page')
        );

        add_submenu_page(
            'video-booking',
            'Orders',
            'Orders',
            'manage_options',
            'video-booking-orders',
            array($this, 'orders_page')
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook)
    {
        if (strpos($hook, 'video-booking') !== false) {
            wp_enqueue_media();
            wp_enqueue_script('video-booking-admin', VIDEO_BOOKING_URL . 'assets/js/video-admin.js', array('jquery'), VIDEO_BOOKING_VERSION, true);
            wp_enqueue_style('video-booking-admin', VIDEO_BOOKING_URL . 'assets/css/video-admin.css', array(), VIDEO_BOOKING_VERSION);

            wp_localize_script('video-booking-admin', 'videoAdmin', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('video_admin_nonce'),
            ));
        }
    }

    /**
     * Main admin page - list all videos
     */
    public function admin_page()
    {
        $videos = VideoBookingDB::get_videos();
?>
        <div class="wrap">
            <h1 class="wp-heading-inline">Video Workshops</h1>
            <a href="<?php echo admin_url('admin.php?page=video-booking-add'); ?>" class="page-title-action">Add New Video</a>
            <hr class="wp-header-end">

            <div class="video-admin-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Thumbnail</th>
                            <th>Title</th>
                            <th>Pricing</th>
                            <th>Status</th>
                            <th>Upload Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($videos)): ?>
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px;">
                                    <p>No videos found. <a href="<?php echo admin_url('admin.php?page=video-booking-add'); ?>">Add your first video</a></p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($videos as $video): ?>
                                <tr data-video-id="<?php echo $video->id; ?>">
                                    <td>
                                        <?php if ($video->thumbnail): ?>
                                            <img src="<?php echo esc_url($video->thumbnail); ?>" alt="" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                                        <?php else: ?>
                                            <div style="width: 60px; height: 40px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">No Image</div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo esc_html($video->title); ?></strong>
                                        <?php if ($video->is_early_bird): ?>
                                            <span class="early-bird-badge">Early Bird</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>Regular: ₹<?php echo number_format($video->regular_price, 2); ?></div>
                                        <?php if ($video->is_early_bird): ?>
                                            <div>Early Bird: ₹<?php echo number_format($video->early_bird_price, 2); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $video->is_available ? 'active' : 'inactive'; ?>">
                                            <?php echo $video->is_available ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="upload-status upload-<?php echo $video->upload_status; ?>">
                                            <?php echo ucfirst($video->upload_status); ?>
                                        </span>
                                        <?php if ($video->is_early_bird && $video->upload_status === 'pending'): ?>
                                            <button class="button button-small mark-uploaded" data-video-id="<?php echo $video->id; ?>">Mark as Uploaded</button>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($video->created_at)); ?></td>
                                    <td>
                                        <a href="<?php echo admin_url('admin.php?page=video-booking-add&edit=' . $video->id); ?>" class="button button-small">Edit</a>
                                        <button class="button button-small toggle-status" data-video-id="<?php echo $video->id; ?>">
                                            <?php echo $video->is_available ? 'Deactivate' : 'Activate'; ?>
                                        </button>
                                        <button class="button button-small button-link-delete delete-video" data-video-id="<?php echo $video->id; ?>">Delete</button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <style>
            .early-bird-badge {
                background: #ff6b35;
                color: white;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 11px;
                font-weight: bold;
                text-transform: uppercase;
                margin-left: 8px;
            }

            .status-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }

            .status-active {
                background: #d4edda;
                color: #155724;
            }

            .status-inactive {
                background: #f8d7da;
                color: #721c24;
            }

            .upload-status {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }

            .upload-uploaded {
                background: #d4edda;
                color: #155724;
            }

            .upload-pending {
                background: #fff3cd;
                color: #856404;
            }

            .mark-uploaded {
                margin-left: 8px;
            }
        </style>
    <?php
    }

    /**
     * Add/Edit video page
     */
    public function add_video_page()
    {
        $video = null;
        $is_edit = false;

        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            $video = VideoBookingDB::get_video($_GET['edit']);
            $is_edit = true;
        }

    ?>
        <div class="wrap">
            <h1><?php echo $is_edit ? 'Edit Video' : 'Add New Video'; ?></h1>
            <hr class="wp-header-end">

            <form id="video-form" class="video-form">
                <?php wp_nonce_field('video_admin_nonce', 'nonce'); ?>
                <?php if ($is_edit): ?>
                    <input type="hidden" name="video_id" value="<?php echo $video->id; ?>">
                <?php endif; ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="title">Video Title *</label></th>
                        <td>
                            <input type="text" id="title" name="title" class="regular-text" value="<?php echo $video ? esc_attr($video->title) : ''; ?>" required>
                            <p class="description">Enter the video workshop title</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="description">Description</label></th>
                        <td>
                            <?php
                            $description_content = $video ? $video->description : '';
                            wp_editor($description_content, 'description', array(
                                'textarea_name' => 'description',
                                'media_buttons' => true,
                                'textarea_rows' => 10,
                                'teeny' => false,
                                'dfw' => false,
                                'tinymce' => array(
                                    'resize' => false,
                                    'wp_autoresize_on' => false,
                                    'add_unload_trigger' => false,
                                ),
                                'quicktags' => true
                            ));
                            ?>
                            <p class="description">Describe what this video workshop covers (HTML formatting available)</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="thumbnail">Thumbnail Image</label></th>
                        <td>
                            <div class="thumbnail-upload">
                                <input type="hidden" id="thumbnail" name="thumbnail" value="<?php echo $video ? esc_attr($video->thumbnail) : ''; ?>">
                                <div class="thumbnail-preview">
                                    <?php if ($video && $video->thumbnail): ?>
                                        <img src="<?php echo esc_url($video->thumbnail); ?>" alt="Thumbnail" style="max-width: 200px; height: auto;">
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="button upload-thumbnail">Choose Thumbnail</button>
                                <button type="button" class="button remove-thumbnail" style="<?php echo (!$video || !$video->thumbnail) ? 'display: none;' : ''; ?>">Remove</button>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="regular_price">Regular Price (₹) *</label></th>
                        <td>
                            <input type="number" id="regular_price" name="regular_price" step="0.01" min="0" class="small-text" value="<?php echo $video ? $video->regular_price : ''; ?>" required>
                            <p class="description">Price for regular purchase</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="is_early_bird">Early Bird Option</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="is_early_bird" name="is_early_bird" value="1" <?php echo ($video && $video->is_early_bird) ? 'checked' : ''; ?>>
                                Enable early bird pricing
                            </label>
                        </td>
                    </tr>

                    <tr class="early-bird-fields" style="<?php echo (!$video || !$video->is_early_bird) ? 'display: none;' : ''; ?>">
                        <th scope="row"><label for="early_bird_price">Early Bird Price (₹)</label></th>
                        <td>
                            <input type="number" id="early_bird_price" name="early_bird_price" step="0.01" min="0" class="small-text" value="<?php echo $video ? $video->early_bird_price : ''; ?>">
                            <p class="description">Discounted price for early bird purchase</p>
                        </td>
                    </tr>

                    <tr class="early-bird-fields" style="<?php echo (!$video || !$video->is_early_bird) ? 'display: none;' : ''; ?>">
                        <th scope="row"><label for="early_bird_note">Early Bird Note</label></th>
                        <td>
                            <textarea id="early_bird_note" name="early_bird_note" rows="3" class="large-text"><?php echo $video ? esc_textarea($video->early_bird_note) : ''; ?></textarea>
                            <p class="description">Note to display for early bird purchases (e.g., "Video will be available after workshop completion")</p>
                        </td>
                    </tr>

                    <tr class="early-bird-fields" style="<?php echo (!$video || !$video->is_early_bird) ? 'display: none;' : ''; ?>">
                        <th scope="row"><label for="early_bird_end_date">Early Bird End Date</label></th>
                        <td>
                            <input type="datetime-local" id="early_bird_end_date" name="early_bird_end_date"
                                value="<?php echo $video && $video->early_bird_end_date ? date('Y-m-d\TH:i', strtotime($video->early_bird_end_date)) : ''; ?>">
                            <p class="description">Date and time when early bird pricing expires. Leave empty for no expiration.</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="duration_days">Access Duration (Days) *</label></th>
                        <td>
                            <input type="number" id="duration_days" name="duration_days" min="1" class="small-text" value="<?php echo $video ? $video->duration_days : '30'; ?>" required>
                            <p class="description">Number of days users will have access to the video</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="video_file">Video File</label></th>
                        <td>
                            <div class="video-upload">
                                <input type="hidden" id="video_file" name="video_file" value="<?php echo $video ? esc_attr($video->video_file) : ''; ?>">
                                <input type="file" id="video_file_input" accept="video/*" style="display:none;" />
                                <div class="video-preview">
                                    <?php if ($video && $video->video_file): ?>
                                        <p>Current file: <strong><?php echo basename($video->video_file); ?></strong></p>
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="button upload-video">Upload Video</button>
                                <div class="video-upload-progress" style="display:none; margin-top:8px;">
                                    <progress id="video_upload_progress" max="100" value="0" style="width:300px;"></progress>
                                    <span id="video_upload_status" style="margin-left:8px;">Uploading...</span>
                                </div>
                                <p class="description">Upload the video file to secure storage</p>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="is_available">Status</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="is_available" name="is_available" value="1" <?php echo (!$video || $video->is_available) ? 'checked' : ''; ?>>
                                Make video available for purchase
                            </label>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="submit" class="button button-primary"><?php echo $is_edit ? 'Update Video' : 'Add Video'; ?></button>
                    <a href="<?php echo admin_url('admin.php?page=video-booking'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Toggle early bird fields
                $('#is_early_bird').change(function() {
                    if ($(this).is(':checked')) {
                        $('.early-bird-fields').show();
                    } else {
                        $('.early-bird-fields').hide();
                    }
                });
            });
        </script>
    <?php
    }

    /**
     * Orders page
     */
    public function orders_page()
    {
        global $wpdb;

        $orders_table = $wpdb->prefix . 'video_booking_orders';
        $orders = $wpdb->get_results("SELECT * FROM $orders_table ORDER BY created_at DESC LIMIT 50");

    ?>
        <div class="wrap">
            <h1>Video Orders</h1>
            <hr class="wp-header-end">

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($orders)): ?>
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px;">
                                <p>No orders found.</p>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><strong><?php echo esc_html($order->order_number); ?></strong></td>
                                <td>
                                    <?php echo esc_html($order->customer_name); ?><br>
                                    <small><?php echo esc_html($order->customer_email); ?></small>
                                </td>
                                <td>₹<?php echo number_format($order->total_amount, 2); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $order->payment_status; ?>">
                                        <?php echo ucfirst($order->payment_status); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y g:i A', strtotime($order->created_at)); ?></td>
                                <td>
                                    <button class="button button-small view-order" data-order-id="<?php echo $order->id; ?>">View Details</button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <style>
            .status-completed {
                background: #d4edda;
                color: #155724;
            }

            .status-pending {
                background: #fff3cd;
                color: #856404;
            }

            .status-failed {
                background: #f8d7da;
                color: #721c24;
            }

            .status-refunded {
                background: #d1ecf1;
                color: #0c5460;
            }
        </style>
<?php
    }

    /**
     * AJAX: Save video
     */
    public function save_video()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $video_id = isset($_POST['video_id']) ? absint($_POST['video_id']) : 0;
        $is_edit = $video_id > 0;

        $is_early_bird = isset($_POST['is_early_bird']) ? 1 : 0;

        $data = array(
            'title' => sanitize_text_field($_POST['title']),
            'description' => wp_kses_post($_POST['description']),
            'thumbnail' => esc_url_raw($_POST['thumbnail']),
            'regular_price' => floatval($_POST['regular_price']),
            'is_early_bird' => $is_early_bird,
            'early_bird_price' => floatval($_POST['early_bird_price']),
            'early_bird_note' => sanitize_textarea_field($_POST['early_bird_note']),
            'early_bird_end_date' => !empty($_POST['early_bird_end_date']) ? sanitize_text_field($_POST['early_bird_end_date']) : null,
            'duration_days' => absint($_POST['duration_days']),
            'video_file' => sanitize_text_field($_POST['video_file']),
            'is_available' => isset($_POST['is_available']) ? 1 : 0,
            'upload_status' => $is_early_bird ? 'pending' : 'completed',
        );

        // Validation
        if (empty($data['title'])) {
            wp_send_json_error('Title is required');
        }

        if ($data['regular_price'] < 0) {
            wp_send_json_error('Regular price cannot be negative');
        }

        if ($data['is_early_bird'] && $data['early_bird_price'] >= $data['regular_price']) {
            wp_send_json_error('Early bird price must be less than regular price');
        }

        if ($data['duration_days'] < 1) {
            wp_send_json_error('Duration must be at least 1 day');
        }

        if ($is_edit) {
            $result = VideoBookingDB::update_video($video_id, $data);
            $message = 'Video updated successfully';
        } else {
            $result = VideoBookingDB::create_video($data);
            $message = 'Video created successfully';
        }

        if ($result) {
            wp_send_json_success($message);
        } else {
            wp_send_json_error('Failed to save video');
        }
    }

    /**
     * AJAX: Delete video
     */
    public function delete_video()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $video_id = absint($_POST['video_id']);

        if (!$video_id) {
            wp_send_json_error('Invalid video ID');
        }

        $result = VideoBookingDB::delete_video($video_id);

        if ($result) {
            wp_send_json_success('Video deleted successfully');
        } else {
            wp_send_json_error('Failed to delete video');
        }
    }

    /**
     * AJAX: Toggle video status
     */
    public function toggle_video_status()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $video_id = absint($_POST['video_id']);

        if (!$video_id) {
            wp_send_json_error('Invalid video ID');
        }

        $video = VideoBookingDB::get_video($video_id);
        if (!$video) {
            wp_send_json_error('Video not found');
        }

        $new_status = $video->is_available ? 0 : 1;
        $result = VideoBookingDB::update_video($video_id, array('is_available' => $new_status));

        if ($result) {
            wp_send_json_success(array('is_available' => $new_status));
        } else {
            wp_send_json_error('Failed to update status');
        }
    }

    /**
     * AJAX: Handle video upload
     */
    public function handle_video_upload()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!isset($_FILES['video_file'])) {
            wp_send_json_error('No file uploaded');
        }

        $file = $_FILES['video_file'];

        // Validate file type - support all common video formats
        $allowed_types = array(
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv',
            'video/m4v',
            'video/3gp',
            'video/quicktime',
            'video/x-msvideo', // Alternative MIME for AVI
            'video/x-ms-wmv', // Alternative MIME for WMV
            'video/x-flv', // Alternative MIME for FLV
            'video/x-m4v', // Alternative MIME for M4V
            'application/octet-stream', // For files that might not have proper MIME type
            'application/x-troff-msvideo', // Some systems report AVI as this
            '' // Some systems don't set MIME type at all
        );

        // File extension validation (more reliable than MIME type)
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = array('mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'qt', 'asf', 'mpg', 'mpeg', 'mp2', 'mpe', 'mpv', 'ogg', 'rm', 'rmvb', 'ts', 'vob');

        // Primary validation by file extension (most reliable)
        if (!in_array($file_extension, $allowed_extensions)) {
            wp_send_json_error('Invalid file type. Supported formats: ' . strtoupper(implode(', ', $allowed_extensions)));
        }

        // Log MIME type for debugging if it's unusual
        if (!empty($file['type']) && !in_array($file['type'], $allowed_types)) {
            error_log('Video upload: Unusual MIME type detected: ' . $file['type'] . ' for file: ' . $file['name'] . ' (Extension: ' . $file_extension . ')');
        }

        // Validate file size (server limit may be lower). Default max 500MB, but also respect PHP limits.
        $php_upload_max = wp_convert_hr_to_bytes(ini_get('upload_max_filesize'));
        $php_post_max   = wp_convert_hr_to_bytes(ini_get('post_max_size'));
        $server_limit   = min($php_upload_max ?: PHP_INT_MAX, $php_post_max ?: PHP_INT_MAX);
        $max_size = min(500 * 1024 * 1024, $server_limit);
        if ($file['size'] > $max_size) {
            wp_send_json_error('File too large. Max allowed: ' . size_format($max_size) . '.');
        }

        // Generate unique filename
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'video_' . time() . '_' . wp_generate_password(8, false) . '.' . $file_extension;
        $upload_path = VIDEO_PRIVATE_PATH . $filename;

        // Create directory if it doesn't exist
        if (!file_exists(VIDEO_PRIVATE_PATH)) {
            wp_mkdir_p(VIDEO_PRIVATE_PATH);
        }

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            wp_send_json_success(array(
                'filename' => $filename,
                'path' => $upload_path,
                'url' => VIDEO_PRIVATE_URL . $filename,
            ));
        } else {
            wp_send_json_error('Failed to upload file');
        }
    }

    /**
     * Chunked upload: initialize an upload session
     */
    public function chunk_init()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $original_name = sanitize_file_name($_POST['filename'] ?? 'video.mp4');
        $extension = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        $allowed_extensions = array('mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'qt', 'asf', 'mpg', 'mpeg', 'mp2', 'mpe', 'mpv', 'ogg', 'rm', 'rmvb', 'ts', 'vob');
        if (!in_array($extension, $allowed_extensions)) {
            wp_send_json_error('Invalid file type');
        }

        // Ensure temp dir exists
        $tmp_dir = VIDEO_PRIVATE_PATH . 'tmp/';
        if (!file_exists($tmp_dir)) {
            wp_mkdir_p($tmp_dir);
        }

        $upload_id = wp_generate_password(20, false);
        $session_file = $tmp_dir . $upload_id . '.json';
        $session = array(
            'original' => $original_name,
            'extension' => $extension,
            'created_at' => time(),
            'parts' => array(),
        );
        file_put_contents($session_file, wp_json_encode($session));

        wp_send_json_success(array('upload_id' => $upload_id));
    }

    /**
     * Chunked upload: receive a chunk
     */
    public function chunk_upload()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $upload_id = sanitize_text_field($_POST['upload_id'] ?? '');
        $index = intval($_POST['index'] ?? -1);
        if (!$upload_id || $index < 0) {
            wp_send_json_error('Invalid upload parameters');
        }

        if (!isset($_FILES['chunk'])) {
            wp_send_json_error('No chunk uploaded');
        }

        $tmp_dir = VIDEO_PRIVATE_PATH . 'tmp/';
        if (!file_exists($tmp_dir)) {
            wp_mkdir_p($tmp_dir);
        }
        $chunk_path = $tmp_dir . $upload_id . '_' . $index . '.part';

        if (!move_uploaded_file($_FILES['chunk']['tmp_name'], $chunk_path)) {
            wp_send_json_error('Failed to save chunk');
        }

        // Update session
        $session_file = $tmp_dir . $upload_id . '.json';
        $session = file_exists($session_file) ? json_decode(file_get_contents($session_file), true) : array('parts' => array());
        $session['parts'][$index] = basename($chunk_path);
        file_put_contents($session_file, wp_json_encode($session));

        wp_send_json_success(array('received' => $index));
    }

    /**
     * Chunked upload: finalize and assemble file
     */
    public function chunk_finish()
    {
        check_ajax_referer('video_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $upload_id = sanitize_text_field($_POST['upload_id'] ?? '');
        $total_parts = intval($_POST['total_parts'] ?? 0);
        if (!$upload_id || $total_parts <= 0) {
            wp_send_json_error('Invalid finalize parameters');
        }

        $tmp_dir = VIDEO_PRIVATE_PATH . 'tmp/';
        $session_file = $tmp_dir . $upload_id . '.json';
        if (!file_exists($session_file)) {
            wp_send_json_error('Upload session not found');
        }
        $session = json_decode(file_get_contents($session_file), true);
        $extension = $session['extension'] ?? 'mp4';

        // Validate all parts exist
        for ($i = 0; $i < $total_parts; $i++) {
            $p = $tmp_dir . $upload_id . '_' . $i . '.part';
            if (!file_exists($p)) {
                wp_send_json_error('Missing chunk ' . $i);
            }
        }

        // Create final file
        if (!file_exists(VIDEO_PRIVATE_PATH)) {
            wp_mkdir_p(VIDEO_PRIVATE_PATH);
        }
        $final_name = 'video_' . time() . '_' . wp_generate_password(8, false) . '.' . $extension;
        $final_path = VIDEO_PRIVATE_PATH . $final_name;

        $out = fopen($final_path, 'wb');
        if (!$out) {
            wp_send_json_error('Failed to open output file');
        }

        // Append parts in order
        for ($i = 0; $i < $total_parts; $i++) {
            $part_path = $tmp_dir . $upload_id . '_' . $i . '.part';
            $in = fopen($part_path, 'rb');
            if ($in) {
                while (!feof($in)) {
                    fwrite($out, fread($in, 1048576)); // 1MB buffer
                }
                fclose($in);
                @unlink($part_path);
            } else {
                fclose($out);
                wp_send_json_error('Failed to read chunk ' . $i);
            }
        }
        fclose($out);

        // Cleanup session
        @unlink($session_file);

        wp_send_json_success(array(
            'filename' => $final_name,
            'path' => $final_path,
            'url' => VIDEO_PRIVATE_URL . $final_name,
        ));
    }
}

// Initialize admin
new VideoBookingAdmin();
